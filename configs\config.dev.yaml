server:
  port: "8081"
  mode: "debug"
  read_timeout: 30
  write_timeout: 30

database:
  enabled: true
  host: "localhost"
  port: 3306
  username: "root"
  password: "root"
  database: "go_web_starter_dev"
  charset: "utf8mb4"

redis:
  enabled: true
  host: "localhost"
  port: 6379
  password: ""
  database: 1
#
kafka:
  enabled: true
  brokers:
    - "localhost:9092"
  group_id: "go-web-starter-dev"

logger:
  level: "debug"
  format: "text"
  output: "both"
  filename: "logs/app-dev.log"
  max_size: 50
  max_backups: 5
  max_age: 7
  compress: false

blockchain:
  enabled: true
  network_url: "https://base-mainnet.infura.io/v3/********************************"
  network_name: "ethereum"
  timeout: 30