# Server Configuration
SERVER_PORT=8080
SERVER_MODE=debug

# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_USERNAME=root
DATABASE_PASSWORD=password
DATABASE_DATABASE=go_web_starter
DATABASE_CHARSET=utf8mb4

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_GROUP_ID=go-web-starter

# Logger Configuration
LOGGER_LEVEL=info
LOGGER_FORMAT=json
LOGGER_OUTPUT=stdout
LOGGER_FILENAME=logs/app.log
LOGGER_MAX_SIZE=100
LOGGER_MAX_BACKUPS=3
LOGGER_MAX_AGE=28
LOGGER_COMPRESS=true