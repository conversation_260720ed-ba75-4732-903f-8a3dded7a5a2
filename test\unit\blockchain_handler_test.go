package unit

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"go-web-starter/internal/config"
	"go-web-starter/internal/handler"
	"go-web-starter/internal/infrastructure/logger"
)

func TestBlockChainHandler_DisabledService(t *testing.T) {
	// 创建禁用区块链的配置
	cfg := &config.Config{
		Blockchain: config.BlockchainConfig{
			Enabled: false,
		},
		Logger: config.LoggerConfig{
			Level:  "info",
			Format: "json",
			Output: "stdout",
		},
	}

	// 初始化日志
	log, err := logger.New(&cfg.Logger)
	require.NoError(t, err)

	// 创建区块链处理器
	blockchainHandler, err := handler.NewBlockChainHandler(cfg, log, nil, nil, nil)
	require.NoError(t, err)
	require.NotNil(t, blockchainHandler)

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 注册路由
	router.GET("/blockchain/block/latest", blockchainHandler.GetLatestBlockNumber)
	router.GET("/blockchain/block/:number", blockchainHandler.GetBlockByNumber)
	router.GET("/blockchain/transaction/:hash", blockchainHandler.GetTransactionByHash)
	router.GET("/blockchain/balance/:address", blockchainHandler.GetBalance)

	tests := []struct {
		name           string
		endpoint       string
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "GetLatestBlockNumber - Service Disabled",
			endpoint:       "/blockchain/block/latest",
			expectedStatus: http.StatusServiceUnavailable,
			expectedError:  "Blockchain service is disabled",
		},
		{
			name:           "GetBlockByNumber - Service Disabled",
			endpoint:       "/blockchain/block/123456",
			expectedStatus: http.StatusServiceUnavailable,
			expectedError:  "Blockchain service is disabled",
		},
		{
			name:           "GetTransactionByHash - Service Disabled",
			endpoint:       "/blockchain/transaction/0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
			expectedStatus: http.StatusServiceUnavailable,
			expectedError:  "Blockchain service is disabled",
		},
		{
			name:           "GetBalance - Service Disabled",
			endpoint:       "/blockchain/balance/0x1234567890123456789012345678901234567890",
			expectedStatus: http.StatusServiceUnavailable,
			expectedError:  "Blockchain service is disabled",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建请求
			req, err := http.NewRequest("GET", tt.endpoint, nil)
			require.NoError(t, err)

			// 创建响应记录器
			w := httptest.NewRecorder()

			// 执行请求
			router.ServeHTTP(w, req)

			// 验证状态码
			assert.Equal(t, tt.expectedStatus, w.Code)

			// 解析响应
			var response map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			// 验证错误消息
			assert.Equal(t, tt.expectedError, response["error"])
		})
	}
}

func TestBlockChainHandler_InvalidInputs(t *testing.T) {
	// 创建启用区块链但使用无效URL的配置（这样可以测试输入验证而不需要真实连接）
	cfg := &config.Config{
		Blockchain: config.BlockchainConfig{
			Enabled:     true,
			NetworkURL:  "invalid-url", // 无效URL，会导致连接失败
			NetworkName: "test",
			Timeout:     30,
		},
		Logger: config.LoggerConfig{
			Level:  "info",
			Format: "json",
			Output: "stdout",
		},
	}

	// 初始化日志
	log, err := logger.New(&cfg.Logger)
	require.NoError(t, err)

	// 尝试创建区块链处理器（应该失败）
	blockchainHandler, err := handler.NewBlockChainHandler(cfg, log, nil, nil, nil)
	require.Error(t, err) // 应该因为无效URL而失败
	require.Nil(t, blockchainHandler)
}

func TestBlockChainHandler_ConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      config.BlockchainConfig
		expectError bool
	}{
		{
			name: "Valid Config",
			config: config.BlockchainConfig{
				Enabled:     true,
				NetworkURL:  "https://mainnet.infura.io/v3/test",
				NetworkName: "ethereum",
				Timeout:     30,
			},
			expectError: false,
		},
		{
			name: "Disabled Service",
			config: config.BlockchainConfig{
				Enabled: false,
			},
			expectError: false,
		},
		{
			name: "Missing Network URL",
			config: config.BlockchainConfig{
				Enabled:     true,
				NetworkURL:  "",
				NetworkName: "ethereum",
				Timeout:     30,
			},
			expectError: true,
		},
		{
			name: "Missing Network Name",
			config: config.BlockchainConfig{
				Enabled:     true,
				NetworkURL:  "https://mainnet.infura.io/v3/test",
				NetworkName: "",
				Timeout:     30,
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 直接测试配置验证逻辑
			if tt.expectError {
				// 这些情况应该在配置验证中被捕获
				if tt.config.Enabled && (tt.config.NetworkURL == "" || tt.config.NetworkName == "") {
					assert.True(t, tt.config.NetworkURL == "" || tt.config.NetworkName == "")
				}
			} else {
				// 对于有效配置，验证字段不为空
				if tt.config.Enabled {
					assert.NotEmpty(t, tt.config.NetworkURL)
					assert.NotEmpty(t, tt.config.NetworkName)
				}
			}
		})
	}
}
