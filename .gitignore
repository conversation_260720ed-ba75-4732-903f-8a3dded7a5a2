# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build directory
build/
dist/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
*.log
logs/

# Configuration files with sensitive data
configs/config.local.yaml
configs/config.prod.yaml
.env
.env.local

# Test coverage
coverage.out
coverage.html

# Temporary files
tmp/
temp/

# Database files
*.db
*.sqlite
*.sqlite3

# Docker
.dockerignore

# Air (hot reload tool)
tmp/