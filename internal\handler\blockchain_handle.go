package handler

import (
	"context"
	"fmt"
	"go-web-starter/internal/config"
	"go-web-starter/internal/infrastructure/cache"
	"go-web-starter/internal/infrastructure/database"
	"go-web-starter/internal/infrastructure/logger"
	"go-web-starter/internal/infrastructure/messaging"
	"log"
	_ "math/big"
	"time"

	"github.com/ethereum/go-ethereum/ethclient"
)

type BlockChainHandler struct {
	config    *config.Config
	logger    *logger.Logger
	startTime time.Time
	// optional deps
	db        *database.Database
	cache     cache.CacheService
	messaging messaging.MessagingService
}

func NewBlockChainHandler(cfg *config.Config, log *logger.Logger, db *database.Database, cacheSvc cache.CacheService, msgSvc messaging.MessagingService) *BlockChainHandler {
	return &BlockChainHandler{
		config:    cfg,
		logger:    log,
		startTime: time.Now(),
		db:        db,
		cache:     cacheSvc,
		messaging: msgSvc,
	}
}

// getBlockNumber
func (h *BlockChainHandler) getBlockNumber(ctx context.Context, client *ethclient.Client) (uint64, error) {

	client, err := ethclient.Dial("")
	if err != nil {
		log.Fatalf("Failed to connect to the Ethereum client: %v", err)
	}
	blockNumber, err := client.BlockNumber(ctx)
	if err != nil {
		log.Println("Error getting block number:", err)
		return 0, err
	}
	fmt.Println("Current block number:", blockNumber)
	return blockNumber, nil
}
