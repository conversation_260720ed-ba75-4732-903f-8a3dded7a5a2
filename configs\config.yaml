server:
  port: "8080"
  mode: "debug"
  read_timeout: 30
  write_timeout: 30

database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "password"
  database: "go_web_starter"
  charset: "utf8mb4"
  log_level: "error"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 60

redis:
  host: "localhost"
  port: 6379
  password: ""
  database: 0

kafka:
  brokers:
    - "localhost:9092"
  group_id: "go-web-starter"

logger:
  level: "info"
  format: "json"
  output: "stdout"
  filename: "logs/app.log"
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true